/* eslint-disable */
export default async () => {
    const t = {
        ["./modules/category/entities/category.entity"]: await import("./modules/category/entities/category.entity"),
        ["./modules/location/entities/location.entity"]: await import("./modules/location/entities/location.entity"),
        ["./modules/auth/entities/permission.entity"]: await import("./modules/auth/entities/permission.entity"),
        ["./common/enums/country-code.type"]: await import("./common/enums/country-code.type"),
        ["./modules/auth/enums/user-status.enum"]: await import("./modules/auth/enums/user-status.enum"),
        ["./modules/auth/entities/role.entity"]: await import("./modules/auth/entities/role.entity"),
        ["./common/enums/default-role.enum"]: await import("./common/enums/default-role.enum"),
        ["./modules/location/dtos/create-location.dto"]: await import("./modules/location/dtos/create-location.dto"),
        ["./modules/business/enums/price-range.enum"]: await import("./modules/business/enums/price-range.enum"),
        ["./modules/business/enums/payment-method.enum"]: await import("./modules/business/enums/payment-method.enum"),
        ["./modules/feature/entities/feature.entity"]: await import("./modules/feature/entities/feature.entity"),
        ["./modules/auth/entities/user.entity"]: await import("./modules/auth/entities/user.entity"),
        ["./modules/auth/dtos/auth/auth-response.dto"]: await import("./modules/auth/dtos/auth/auth-response.dto"),
        ["./modules/business/entities/business.entity"]: await import("./modules/business/entities/business.entity"),
        ["./modules/dashboard/dtos/dashboard-statistics.dto"]: await import("./modules/dashboard/dtos/dashboard-statistics.dto")
    };
    return { "@nestjs/swagger": { "models": [[import("./common/dtos/configuration-validation.dto"), { "ConfigurationValidationDto": { PORT: { required: true, type: () => Number }, HOST_URL: { required: true, type: () => String }, NODE_ENV: { required: true, type: () => String }, DATABASE_URL: { required: true, type: () => String }, SWAGGER_PATH: { required: true, type: () => String }, SWAGGER_VERSION: { required: true, type: () => String }, SWAGGER_TITLE: { required: true, type: () => String }, SWAGGER_DESCRIPTION: { required: true, type: () => String }, GOOGLE_CLOUD_LOGGING_ENABLED: { required: false, type: () => Boolean }, GOOGLE_CLOUD_PROJECT_ID: { required: false, type: () => String }, GOOGLE_CLOUD_KEY_FILENAME: { required: false, type: () => String }, GOOGLE_CLOUD_LOG_NAME: { required: false, type: () => String }, JWT_SECRET: { required: true, type: () => String }, JWT_REFRESH_SECRET: { required: true, type: () => String }, JWT_ACCESS_TOKEN_EXPIRATION: { required: true, type: () => String }, JWT_REFRESH_TOKEN_EXPIRATION: { required: true, type: () => String } } }], [import("./common/dtos/get-all-base.dto"), { "GetAllDto": { offset: { required: false, type: () => Number, default: 0 }, limit: { required: false, type: () => Number, default: 10 }, searchKey: { required: false, type: () => String }, sortKey: { required: false, type: () => String, description: "Formatted: {propName}:{-1 | 1},\nFor Example: firstName:-1\nwhere the firstName one of the model properties" }, createdAtFrom: { required: false, type: () => String, description: "Formatted: ISO_8601,\nFor Example: 2020-07-10" }, createdAtTo: { required: false, type: () => String, description: "Formatted: ISO_8601,\nFor Example: 2020-07-10" }, updateAtFrom: { required: false, type: () => String, description: "Formatted: ISO_8601,\nFor Example: 2020-07-10" }, updateAtTo: { required: false, type: () => String, description: "Formatted: ISO_8601,\nFor Example: 2020-07-10" }, withDeleted: { required: false, type: () => Boolean, default: false }, ids: { required: false, type: () => String, description: "Comma separated string of ids\nFor Example: 1,2,3,4" } } }], [import("./common/dtos/init-swagger.dto"), { "InitSwaggerDto": { app: { required: true }, title: { required: true, type: () => String, minLength: 3 }, description: { required: true, type: () => String, minLength: 10 }, version: { required: true, type: () => String, pattern: "/^\\d+\\.\\d+\\.\\d+$/" }, path: { required: true, type: () => String, pattern: "/^[a-zA-Z0-9-_/]+$/" }, hostURL: { required: true, type: () => String, format: "uri" } } }], [import("./common/entities/base.entity"), { "BaseEntity": { id: { required: true, type: () => Number }, createdAt: { required: false, type: () => Date }, updatedAt: { required: false, type: () => Date } } }], [import("./modules/category/entities/category.entity"), { "Category": { nameEn: { required: true, type: () => String }, nameAr: { required: true, type: () => String }, descriptionEn: { required: false, type: () => String }, descriptionAr: { required: false, type: () => String }, icon: { required: false, type: () => String }, cover: { required: false, type: () => String, format: "uri" }, slug: { required: false, type: () => String }, numberOfBusinesses: { required: true, type: () => Number, default: 0, minimum: 0 }, sortOrder: { required: true, type: () => Number, default: 0, minimum: 0 }, level: { required: true, type: () => Number, default: 0 }, path: { required: false, type: () => String }, parentId: { required: false, type: () => Number }, parent: { required: false, type: () => t["./modules/category/entities/category.entity"].Category }, children: { required: false, type: () => [t["./modules/category/entities/category.entity"].Category] } } }], [import("./modules/location/entities/location.entity"), { "Location": { nameEn: { required: true, type: () => String }, nameAr: { required: true, type: () => String }, latitude: { required: true, type: () => Number, minimum: -90, maximum: 90 }, longitude: { required: true, type: () => Number, minimum: -180, maximum: 180 }, streetAddressEn: { required: false, type: () => String }, streetAddressAr: { required: false, type: () => String }, cityEn: { required: false, type: () => String }, cityAr: { required: false, type: () => String }, stateEn: { required: false, type: () => String }, stateAr: { required: false, type: () => String }, countryEn: { required: false, type: () => String }, countryAr: { required: false, type: () => String }, postalCode: { required: false, type: () => String }, nearestLandmarkEn: { required: false, type: () => String }, nearestLandmarkAr: { required: false, type: () => String }, descriptionEn: { required: false, type: () => String }, descriptionAr: { required: false, type: () => String }, mapUrl: { required: false, type: () => String, format: "uri" }, placeId: { required: false, type: () => String }, accuracy: { required: false, type: () => Number, minimum: 0, maximum: 5 }, altitude: { required: false, type: () => Number, minimum: 0 }, timezone: { required: false, type: () => String }, parent: { required: false, type: () => t["./modules/location/entities/location.entity"].Location }, parentId: { required: false, type: () => Number }, children: { required: false, type: () => [t["./modules/location/entities/location.entity"].Location] } } }], [import("./modules/auth/entities/permission.entity"), { "Permission": { action: { required: true, type: () => String }, manuallyAdded: { required: true, type: () => Boolean } } }], [import("./modules/auth/entities/role.entity"), { "Role": { name: { required: true, type: () => String }, permissions: { required: true, type: () => [t["./modules/auth/entities/permission.entity"].Permission] } } }], [import("./modules/location/dtos/create-location.dto"), { "CreateLocationDto": {} }], [import("./modules/feature/entities/feature.entity"), { "Feature": { nameEn: { required: true, type: () => String }, nameAr: { required: true, type: () => String }, icon: { required: true, type: () => String }, businesses: { required: false, type: () => [Object] } } }], [import("./modules/auth/entities/user.entity"), { "User": { firstName: { required: true, type: () => String }, lastName: { required: false, type: () => String }, phoneNumber: { required: false, type: () => String }, countryCode: { required: false, enum: t["./common/enums/country-code.type"].CountryDialCode }, email: { required: false, type: () => String, format: "email" }, apiKey: { required: false, type: () => String }, password: { required: true, type: () => String, minLength: 4, maxLength: 20, pattern: "/((?=.*\\d)|(?=.*\\W+))(?![.\\n])(?=.*[A-Z])(?=.*[a-z]).*$/" }, age: { required: false, type: () => Number }, birthDate: { required: false, type: () => Date }, status: { required: true, enum: t["./modules/auth/enums/user-status.enum"].UserStatus }, roleId: { required: true, type: () => Number }, role: { required: false, type: () => t["./modules/auth/entities/role.entity"].Role } } }], [import("./modules/auth/dtos/auth/auth-response.dto"), { "AuthResponseDto": { accessToken: { required: true, type: () => String }, refreshToken: { required: true, type: () => String }, tokenType: { required: true, type: () => String }, expiresIn: { required: true, type: () => Number } } }], [import("./modules/auth/dtos/auth/login.dto"), { "LoginDto": { loginId: { required: true, type: () => String }, password: { required: true, type: () => String }, roleName: { required: false, enum: t["./common/enums/default-role.enum"].DefaultRole } } }], [import("./modules/auth/dtos/auth/refresh-token.dto"), { "RefreshTokenDto": { refreshToken: { required: true, type: () => String } } }], [import("./modules/auth/dtos/auth/register.dto"), { "RegisterDto": { email: { required: true, type: () => String, format: "email" }, password: { required: true, type: () => String }, firstName: { required: true, type: () => String, minLength: 1, maxLength: 50 }, lastName: { required: true, type: () => String, minLength: 1, maxLength: 50 }, phoneNumber: { required: false, type: () => String }, phoneNumberRegion: { required: false, enum: t["./common/enums/country-code.type"].CountryDialCode } } }], [import("./modules/auth/dtos/role/check-role-contains-permission.dto"), { "CheckRoleContainsPermissionDto": { roleId: { required: true, type: () => Number }, action: { required: true, type: () => String } } }], [import("./modules/auth/dtos/role/create-role.dto"), { "CreateRoleDto": { permissions: { required: true, type: () => [Number] } } }], [import("./modules/auth/dtos/role/get-all-role.dto"), { "GetAllRoleDto": {} }], [import("./modules/auth/dtos/role/update-role.dto"), { "UpdateRoleDto": { permissions: { required: true, type: () => [Number] } } }], [import("./modules/auth/dtos/user/create-user.dto"), { "CreateUserDto": {} }], [import("./modules/auth/dtos/user/get-all-user.dto"), { "GetAllUserDto": { status: { required: false, enum: t["./modules/auth/enums/user-status.enum"].UserStatus }, roleId: { required: false, type: () => Number }, userId: { required: false, type: () => Number } } }], [import("./modules/auth/dtos/user/update-user.dto"), { "UpdateUserDto": { parentId: { required: false, type: () => String } } }], [import("./modules/auth/dtos/user/validate-user.dto"), { "ValidateUserDto": { loginId: { required: true, type: () => String }, passwordValidation: { required: true, type: () => Boolean }, roleName: { required: false, enum: t["./common/enums/default-role.enum"].DefaultRole } } }], [import("./modules/auth/dtos/permission/create-permissions.dto"), { "CreatePermissionsDto": {} }], [import("./modules/auth/dtos/permission/get-all-permissions.dto"), { "GetAllPermissionsDto": {} }], [import("./modules/auth/dtos/permission/update-permission.dto"), { "UpdatePermissionDto": {} }], [import("./modules/category/dtos/create-category.dto"), { "CreateCategoryDto": {} }], [import("./modules/category/dtos/get-all-category.dto"), { "GetAllCategoryDto": { parentId: { required: false, type: () => Number }, level: { required: false, type: () => Number }, includeChildren: { required: false, type: () => Boolean } } }], [import("./modules/category/dtos/update-category.dto"), { "UpdateCategoryDto": {} }], [import("./modules/feature/dtos/create-feature.dto"), { "CreateFeatureDto": {} }], [import("./modules/feature/dtos/get-all-feature.dto"), { "GetAllFeatureDto": {} }], [import("./modules/feature/dtos/update-feature.dto"), { "UpdateFeatureDto": {} }], [import("./modules/location/dtos/create-public-location.dto"), { "CreatePublicLocationDto": {} }], [import("./modules/location/dtos/get-all-location.dto"), { "GetAllLocationDto": { minLatitude: { required: false, type: () => Number, minimum: -90, maximum: 90 }, maxLatitude: { required: false, type: () => Number, minimum: -90, maximum: 90 }, minLongitude: { required: false, type: () => Number, minimum: -180, maximum: 180 }, maxLongitude: { required: false, type: () => Number, minimum: -180, maximum: 180 }, centerLatitude: { required: false, type: () => Number, minimum: -90, maximum: 90 }, centerLongitude: { required: false, type: () => Number, minimum: -180, maximum: 180 }, radiusKm: { required: false, type: () => Number, minimum: 0 }, cityEn: { required: false, type: () => String }, cityAr: { required: false, type: () => String }, countryEn: { required: false, type: () => String }, countryAr: { required: false, type: () => String }, parentId: { required: false, type: () => Number } } }], [import("./modules/location/dtos/update-location.dto"), { "UpdateLocationDto": {} }], [import("./modules/business/dtos/create-business.dto"), { "CreateBusinessDto": { locationId: { required: false, type: () => Number }, location: { required: false, type: () => t["./modules/location/dtos/create-location.dto"].CreateLocationDto }, featureIds: { required: false, type: () => [Number] } } }], [import("./modules/business/dtos/create-public-business.dto"), { "CreatePublicBusinessDto": {} }], [import("./modules/business/dtos/get-all-business.dto"), { "GetAllBusinessDto": { locationId: { required: false, type: () => Number }, primaryCategoryId: { required: false, type: () => Number }, ownerUserId: { required: false, type: () => Number }, featureId: { required: false, type: () => Number }, priceRange: { required: false, enum: t["./modules/business/enums/price-range.enum"].PriceRange }, paymentMethod: { required: false, enum: t["./modules/business/enums/payment-method.enum"].PaymentMethod }, isActive: { required: false, type: () => Boolean }, isVerified: { required: false, type: () => Boolean }, isPremium: { required: false, type: () => Boolean }, isOpen24x7: { required: false, type: () => Boolean }, minRating: { required: false, type: () => Number }, cityEn: { required: false, type: () => String }, cityAr: { required: false, type: () => String }, includeLocation: { required: false, type: () => Boolean }, includeCategory: { required: false, type: () => Boolean }, includeFeatures: { required: false, type: () => Boolean }, includeOwner: { required: false, type: () => Boolean } } }], [import("./modules/business/dtos/update-business.dto"), { "UpdateBusinessDto": {} }], [import("./modules/business/entities/business.entity"), { "Business": { nameAr: { required: true, type: () => String }, nameEn: { required: true, type: () => String }, phoneNumber: { required: false, type: () => String }, whatsAppNumber: { required: false, type: () => String }, priceRange: { required: false, enum: t["./modules/business/enums/price-range.enum"].PriceRange }, locationId: { required: true, type: () => Number }, primaryCategoryId: { required: true, type: () => Number }, operatingHours: { required: false, type: () => Object }, isOpen24x7: { required: false, type: () => Boolean, default: false }, ramadanHours: { required: false, type: () => Object }, logoUrl: { required: false, type: () => String, format: "uri" }, coverPhotoUrl: { required: false, type: () => String, format: "uri" }, galleryUrls: { required: false, type: () => [String], maximum: 10, format: "uri" }, shortDescriptionAr: { required: false, type: () => String, maxLength: 160 }, shortDescriptionEn: { required: false, type: () => String, maxLength: 160 }, fullDescriptionAr: { required: false, type: () => String }, fullDescriptionEn: { required: false, type: () => String }, isActive: { required: false, type: () => Boolean, default: false }, isVerified: { required: false, type: () => Boolean, default: false }, isPremium: { required: false, type: () => Boolean, default: false }, premiumExpiresAt: { required: false, type: () => Date }, averageRating: { required: false, type: () => Number, default: 0, minimum: 0, maximum: 5 }, totalReviewsCount: { required: false, type: () => Number, default: 0, minimum: 0 }, totalViewsCount: { required: false, type: () => Number, default: 0, minimum: 0 }, lastMonthViews: { required: false, type: () => Number, default: 0, minimum: 0 }, ownerUserId: { required: false, type: () => Number }, paymentMethods: { required: false, enum: t["./modules/business/enums/payment-method.enum"].PaymentMethod, isArray: true }, location: { required: false, type: () => t["./modules/location/entities/location.entity"].Location }, primaryCategory: { required: false, type: () => t["./modules/category/entities/category.entity"].Category }, features: { required: false, type: () => [t["./modules/feature/entities/feature.entity"].Feature] }, owner: { required: false, type: () => t["./modules/auth/entities/user.entity"].User } } }], [import("./modules/dashboard/dtos/dashboard-statistics.dto"), { "DashboardStatisticsDto": { categoriesCount: { required: true, type: () => Number }, featuresCount: { required: true, type: () => Number }, locationsCount: { required: true, type: () => Number }, businessesCount: { required: true, type: () => Number }, activeBusinessesCount: { required: true, type: () => Number }, verifiedBusinessesCount: { required: true, type: () => Number }, premiumBusinessesCount: { required: true, type: () => Number } } }]], "controllers": [[import("./modules/auth/controllers/auth.controller"), { "AuthController": { "register": { type: t["./modules/auth/dtos/auth/auth-response.dto"].AuthResponseDto }, "login": { type: t["./modules/auth/dtos/auth/auth-response.dto"].AuthResponseDto }, "refresh": { type: t["./modules/auth/dtos/auth/auth-response.dto"].AuthResponseDto } } }], [import("./modules/auth/controllers/permission.controller"), { "PermissionController": { "getAll": { summary: "Get all permissions with pagination and filtering" }, "getById": { summary: "Get permission by ID", type: t["./modules/auth/entities/permission.entity"].Permission }, "create": { summary: "Create new permission", type: t["./modules/auth/entities/permission.entity"].Permission }, "update": { summary: "Update an existing permission", type: t["./modules/auth/entities/permission.entity"].Permission }, "delete": { summary: "Delete a permission" }, "findOrCreate": { summary: "Finds a permission by action or creates a new one if it doesn't exist", type: t["./modules/auth/entities/permission.entity"].Permission } } }], [import("./modules/auth/controllers/role.controller"), { "RoleController": { "getAll": { summary: "Get all roles with pagination and filtering" }, "getById": { summary: "Get role by ID", type: t["./modules/auth/entities/role.entity"].Role }, "create": { summary: "Create new role", type: t["./modules/auth/entities/role.entity"].Role }, "update": { summary: "Update an existing role", type: t["./modules/auth/entities/role.entity"].Role }, "delete": { summary: "Delete a role" }, "findOneWithPermissions": { summary: "Retrieves a role with its permissions by ID", type: t["./modules/auth/entities/role.entity"].Role }, "addPermission": { summary: "Adds a new permission to a role", type: Object } } }], [import("./modules/auth/controllers/user.controller"), { "UserController": { "getProfile": { type: Object }, "getAll": { summary: "Get all users with pagination and filtering" }, "getById": { summary: "Get user by ID", type: t["./modules/auth/entities/user.entity"].User }, "create": { summary: "Create new user", type: t["./modules/auth/entities/user.entity"].User }, "update": { summary: "Update an existing user", type: t["./modules/auth/entities/user.entity"].User }, "delete": { summary: "Delete a user" } } }], [import("./modules/category/controllers/category.controller"), { "CategoryController": { "getAll": {}, "getHierarchy": { type: [t["./modules/category/entities/category.entity"].Category] }, "getByParentId": { type: [t["./modules/category/entities/category.entity"].Category] }, "getByLevel": { type: [t["./modules/category/entities/category.entity"].Category] }, "getById": { type: t["./modules/category/entities/category.entity"].Category }, "getWithChildren": { type: Object }, "create": { type: t["./modules/category/entities/category.entity"].Category }, "update": { type: t["./modules/category/entities/category.entity"].Category }, "updateBusinessCount": {}, "delete": {} } }], [import("./modules/location/controllers/location.controller"), { "LocationController": { "createPublic": { type: t["./modules/location/entities/location.entity"].Location }, "getAllPublic": {}, "getAll": {}, "getByCoordinates": { type: [t["./modules/location/entities/location.entity"].Location] }, "getWithinBounds": { type: [t["./modules/location/entities/location.entity"].Location] }, "getNearest": { type: [t["./modules/location/entities/location.entity"].Location] }, "getByCity": { type: [t["./modules/location/entities/location.entity"].Location] }, "getByCountry": { type: [t["./modules/location/entities/location.entity"].Location] }, "getByParentId": { type: [t["./modules/location/entities/location.entity"].Location] }, "calculateDistance": {}, "getById": { type: t["./modules/location/entities/location.entity"].Location }, "getWithChildren": { type: Object }, "create": { type: t["./modules/location/entities/location.entity"].Location }, "update": { type: t["./modules/location/entities/location.entity"].Location }, "delete": {} } }], [import("./modules/business/controllers/business.controller"), { "BusinessController": { "createPublic": { type: t["./modules/business/entities/business.entity"].Business }, "getAll": {}, "getByLocationId": { type: [t["./modules/business/entities/business.entity"].Business] }, "getByPrimaryCategoryId": { type: [t["./modules/business/entities/business.entity"].Business] }, "getByOwnerUserId": { type: [t["./modules/business/entities/business.entity"].Business] }, "getByFeatureId": { type: [t["./modules/business/entities/business.entity"].Business] }, "getByRatingRange": { type: [t["./modules/business/entities/business.entity"].Business] }, "getByCity": { type: [t["./modules/business/entities/business.entity"].Business] }, "getNearby": { type: [t["./modules/business/entities/business.entity"].Business] }, "getActiveBusinesses": { type: [t["./modules/business/entities/business.entity"].Business] }, "getVerifiedBusinesses": { type: [t["./modules/business/entities/business.entity"].Business] }, "getPremiumBusinesses": { type: [t["./modules/business/entities/business.entity"].Business] }, "getById": { type: t["./modules/business/entities/business.entity"].Business }, "create": { type: t["./modules/business/entities/business.entity"].Business }, "update": { type: t["./modules/business/entities/business.entity"].Business }, "activate": { type: t["./modules/business/entities/business.entity"].Business }, "deactivate": { type: t["./modules/business/entities/business.entity"].Business }, "verify": { type: t["./modules/business/entities/business.entity"].Business }, "unverify": { type: t["./modules/business/entities/business.entity"].Business }, "upgradePremium": { type: t["./modules/business/entities/business.entity"].Business }, "downgradePremium": { type: t["./modules/business/entities/business.entity"].Business }, "delete": {} } }], [import("./modules/feature/controllers/feature.controller"), { "FeatureController": { "getAll": { summary: "Get all features with pagination and filtering" }, "getById": { summary: "Get feature by ID", type: t["./modules/feature/entities/feature.entity"].Feature }, "create": { summary: "Create new feature", type: t["./modules/feature/entities/feature.entity"].Feature }, "update": { summary: "Update an existing feature", type: t["./modules/feature/entities/feature.entity"].Feature }, "delete": { summary: "Delete a feature" } } }], [import("./modules/dashboard/controllers/dashboard.controller"), { "DashboardController": { "getStatistics": { type: t["./modules/dashboard/dtos/dashboard-statistics.dto"].DashboardStatisticsDto } } }]] } };
};