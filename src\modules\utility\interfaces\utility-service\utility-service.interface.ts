import { Business } from '../../../business/entities/business.entity';

export interface ImportPlacesParams {
  latitude: number;
  longitude: number;
  radius: number;
  categoryIds?: number[];
}

export interface ImportPlacesResult {
  totalProcessed: number;
  businessesCreated: number;
  locationsCreated: number;
  featuresCreated: number;
  errors: string[];
}

export interface IUtilityService {
  importPlacesFromGooglePlaces(params: ImportPlacesParams): Promise<ImportPlacesResult>;
}

export const IUtilityService = Symbol('IUtilityService');

