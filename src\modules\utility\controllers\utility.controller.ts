import { Public } from '@app/common/decorators';
import {
  Body,
  Controller,
  HttpCode,
  HttpStatus,
  Inject,
  Post,
} from '@nestjs/common';
import { ApiExcludeController } from '@nestjs/swagger';
import { ImportPlacesDto, ImportPlacesResponseDto } from '../dtos/import-places.dto';
import { IUtilityService } from '../interfaces/utility-service/utility-service.interface';

@Controller('utility')
@ApiExcludeController() // Hide all routes from Swagger documentation
@Public() // Make all routes public (no authentication required)
export class UtilityController {
  constructor(
    @Inject(IUtilityService)
    private readonly utilityService: IUtilityService,
  ) {}

  @Post('import-places')
  @HttpCode(HttpStatus.OK)
  async importPlacesFromGooglePlaces(
    @Body() importPlacesDto: ImportPlacesDto,
  ): Promise<ImportPlacesResponseDto> {
    try {
      const result = await this.utilityService.importPlacesFromGooglePlaces({
        latitude: importPlacesDto.latitude,
        longitude: importPlacesDto.longitude,
        radius: importPlacesDto.radius,
        categoryIds: importPlacesDto.categoryIds,
      });

      return {
        totalProcessed: result.totalProcessed,
        businessesCreated: result.businessesCreated,
        locationsCreated: result.locationsCreated,
        featuresCreated: result.featuresCreated,
        errors: result.errors,
      };
    } catch (error) {
      // Log the error for monitoring
      console.error('Error importing places from Google Places API:', error);
      
      // Return error response
      return {
        totalProcessed: 0,
        businessesCreated: 0,
        locationsCreated: 0,
        featuresCreated: 0,
        errors: [error.message || 'An unexpected error occurred during import'],
      };
    }
  }
}
