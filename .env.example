# Server Configuration
PORT=3000
HOST_URL=http://localhost:3000
NODE_ENV=development

# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/qareeb

# Swagger Configuration
SWAGGER_PATH=api-docs
SWAGGER_VERSION=1.0.0
SWAGGER_TITLE=Qareeb API
SWAGGER_DESCRIPTION=Qareeb API Documentation

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-this-in-production
JWT_ACCESS_TOKEN_EXPIRATION=1h
JWT_REFRESH_TOKEN_EXPIRATION=7d

# Google Cloud Logging Configuration (Optional)
GOOGLE_CLOUD_LOGGING_ENABLED=false
GOOGLE_CLOUD_PROJECT_ID=
GOOGLE_CLOUD_KEY_FILENAME=
GOOGLE_CLOUD_LOG_NAME=