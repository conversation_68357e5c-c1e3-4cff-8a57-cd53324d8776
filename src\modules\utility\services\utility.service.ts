import { ConfigurationValidationDto } from '@app/common';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Business } from '../../business/entities/business.entity';
import { IBusinessService } from '../../business/interfaces/business-service/business-service.interface';
import { Category } from '../../category/entities/category.entity';
import { ICategoryService } from '../../category/interfaces/category-service/category-service.interface';
import { Feature } from '../../feature/entities/feature.entity';
import { IFeatureService } from '../../feature/interfaces/feature-service/feature-service.interface';
import { Location } from '../../location/entities/location.entity';
import { ILocationService } from '../../location/interfaces/location-service/location-service.interface';
import {
  ImportPlacesParams,
  ImportPlacesResult,
  IUtilityService,
} from '../interfaces/utility-service/utility-service.interface';

interface GooglePlace {
  place_id: string;
  name: string;
  formatted_address: string;
  geometry: {
    location: {
      lat: number;
      lng: number;
    };
  };
  types: string[];
  rating?: number;
  user_ratings_total?: number;
  formatted_phone_number?: string;
  website?: string;
  opening_hours?: {
    open_now: boolean;
    periods?: Array<{
      open: { day: number; time: string };
      close?: { day: number; time: string };
    }>;
  };
  photos?: Array<{
    photo_reference: string;
    height: number;
    width: number;
  }>;
}

interface GooglePlacesResponse {
  results: GooglePlace[];
  status: string;
  next_page_token?: string;
  error_message?: string;
}

@Injectable()
export class UtilityService implements IUtilityService {
  private readonly logger = new Logger(UtilityService.name);
  private readonly googlePlacesApiKey: string;
  private readonly googlePlacesBaseUrl: string;
  private readonly rateLimitDelay = 100; // 100ms delay between requests
  private readonly maxRetries = 3;

  constructor(
    private readonly configService: ConfigService<
      ConfigurationValidationDto,
      true
    >,
    @Inject(IBusinessService)
    private readonly businessService: IBusinessService,
    @Inject(ILocationService)
    private readonly locationService: ILocationService,
    @Inject(ICategoryService)
    private readonly categoryService: ICategoryService,
    @Inject(IFeatureService)
    private readonly featureService: IFeatureService,
  ) {
    this.googlePlacesApiKey =
      this.configService.get('GOOGLE_PLACES_API_KEY') || '';
    this.googlePlacesBaseUrl =
      this.configService.get('GOOGLE_PLACES_API_BASE_URL') ||
      'https://maps.googleapis.com/maps/api/place';

    if (!this.googlePlacesApiKey) {
      this.logger.warn(
        'Google Places API key not configured. Import functionality will not work.',
      );
    }
  }

  async importPlacesFromGooglePlaces(
    params: ImportPlacesParams,
  ): Promise<ImportPlacesResult> {
    this.logger.log(
      `Starting Google Places import for coordinates: ${params.latitude}, ${params.longitude}`,
    );

    if (!this.googlePlacesApiKey) {
      throw new Error('Google Places API key not configured');
    }

    const result: ImportPlacesResult = {
      totalProcessed: 0,
      businessesCreated: 0,
      locationsCreated: 0,
      featuresCreated: 0,
      errors: [],
    };

    try {
      // Get categories to process
      const categories = await this.getCategoriesToProcess(params.categoryIds);
      this.logger.log(`Processing ${categories.length} categories`);

      // Process each category
      for (const category of categories) {
        try {
          await this.processCategoryPlaces(category, params, result);

          // Rate limiting delay between categories
          await this.delay(this.rateLimitDelay * 2);
        } catch (error) {
          const errorMsg = `Error processing category ${category.nameEn}: ${error.message}`;
          this.logger.error(errorMsg);
          result.errors.push(errorMsg);
        }
      }

      this.logger.log(
        `Import completed. Processed: ${result.totalProcessed}, Created: ${result.businessesCreated} businesses`,
      );
      return result;
    } catch (error) {
      this.logger.error('Fatal error during import:', error);
      result.errors.push(`Fatal error: ${error.message}`);
      return result;
    }
  }

  private async getCategoriesToProcess(
    categoryIds?: number[],
  ): Promise<Category[]> {
    if (categoryIds && categoryIds.length > 0) {
      const categories: Category[] = [];
      for (const id of categoryIds) {
        try {
          const category = await this.categoryService.findOneById(id);
          if (category) {
            categories.push(category);
          }
        } catch (error) {
          this.logger.warn(`Category with ID ${id} not found`);
        }
      }
      return categories;
    } else {
      // Get all categories
      const allCategories = await this.categoryService.getAll({
        limit: 1000,
        offset: 0,
      });
      return allCategories.items;
    }
  }

  private async processCategoryPlaces(
    category: Category,
    params: ImportPlacesParams,
    result: ImportPlacesResult,
  ): Promise<void> {
    this.logger.log(`Processing places for category: ${category.nameEn}`);

    // Map category to Google Places types
    const placeTypes = this.mapCategoryToGoogleTypes(category);

    for (const placeType of placeTypes) {
      try {
        const places = await this.fetchGooglePlaces(
          params.latitude,
          params.longitude,
          params.radius,
          placeType,
        );

        for (const place of places) {
          try {
            await this.processPlace(place, category, result);
            result.totalProcessed++;

            // Rate limiting delay between places
            await this.delay(this.rateLimitDelay);
          } catch (error) {
            const errorMsg = `Error processing place ${place.name}: ${error.message}`;
            this.logger.error(errorMsg);
            result.errors.push(errorMsg);
          }
        }
      } catch (error) {
        const errorMsg = `Error fetching places for type ${placeType}: ${error.message}`;
        this.logger.error(errorMsg);
        result.errors.push(errorMsg);
      }
    }
  }

  private async fetchGooglePlaces(
    latitude: number,
    longitude: number,
    radius: number,
    placeType: string,
  ): Promise<GooglePlace[]> {
    const url = `${this.googlePlacesBaseUrl}/nearbysearch/json`;
    const params = new URLSearchParams({
      location: `${latitude},${longitude}`,
      radius: (radius * 1000).toString(), // Convert km to meters
      type: placeType,
      key: this.googlePlacesApiKey,
    });

    let allPlaces: GooglePlace[] = [];
    let nextPageToken: string | undefined;
    let attempts = 0;

    do {
      if (nextPageToken) {
        params.set('pagetoken', nextPageToken);
        // Google requires a delay before using next page token
        await this.delay(2000);
      }

      const response = await this.makeGooglePlacesRequest(
        `${url}?${params.toString()}`,
      );

      if (response.status !== 'OK' && response.status !== 'ZERO_RESULTS') {
        throw new Error(
          `Google Places API error: ${response.status} - ${response.error_message || 'Unknown error'}`,
        );
      }

      allPlaces.push(...response.results);
      nextPageToken = response.next_page_token;
      attempts++;

      // Limit to prevent infinite loops and respect API quotas
      if (attempts >= 3) {
        break;
      }
    } while (nextPageToken);

    return allPlaces;
  }

  private async makeGooglePlacesRequest(
    url: string,
  ): Promise<GooglePlacesResponse> {
    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        const response = await fetch(url);

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return await response.json();
      } catch (error) {
        this.logger.warn(
          `Attempt ${attempt} failed for Google Places request: ${error.message}`,
        );

        if (attempt === this.maxRetries) {
          throw error;
        }

        // Exponential backoff
        await this.delay(1000 * Math.pow(2, attempt - 1));
      }
    }

    throw new Error('Max retries exceeded');
  }

  private mapCategoryToGoogleTypes(category: Category): string[] {
    // Map category names to Google Places types
    const categoryName = category.nameEn.toLowerCase();

    const typeMapping: Record<string, string[]> = {
      restaurant: ['restaurant', 'food'],
      food: ['restaurant', 'food', 'meal_takeaway'],
      cafe: ['cafe'],
      hotel: ['lodging'],
      hospital: ['hospital'],
      pharmacy: ['pharmacy'],
      gas_station: ['gas_station'],
      bank: ['bank'],
      atm: ['atm'],
      shopping: ['shopping_mall', 'store'],
      supermarket: ['supermarket'],
      gym: ['gym'],
      beauty: ['beauty_salon', 'hair_care'],
      car_repair: ['car_repair'],
      school: ['school'],
      university: ['university'],
      mosque: ['mosque'],
      church: ['church'],
      park: ['park'],
      tourist_attraction: ['tourist_attraction'],
    };

    // Try to find matching types
    for (const [key, types] of Object.entries(typeMapping)) {
      if (categoryName.includes(key)) {
        return types;
      }
    }

    // Default fallback
    return ['establishment'];
  }

  private async processPlace(
    place: GooglePlace,
    category: Category,
    result: ImportPlacesResult,
  ): Promise<void> {
    try {
      // Check if business already exists by place_id
      const existingLocation = await this.locationService.findOneBy({
        where: { placeId: place.place_id },
      });

      if (existingLocation) {
        this.logger.debug(`Place ${place.name} already exists, skipping`);
        return;
      }

      // Create or find location
      const location = await this.createOrFindLocation(place, result);

      // Extract features from place types
      const features = await this.extractAndCreateFeatures(place, result);

      // Create business
      const business = await this.createBusiness(
        place,
        category,
        location,
        features,
        result,
      );

      this.logger.debug(`Successfully created business: ${business.nameEn}`);
    } catch (error) {
      throw new Error(
        `Failed to process place ${place.name}: ${error.message}`,
      );
    }
  }

  private async createOrFindLocation(
    place: GooglePlace,
    result: ImportPlacesResult,
  ): Promise<Location> {
    try {
      // Check if location exists by coordinates (within small tolerance)
      const tolerance = 0.0001; // ~11 meters
      const existingLocations = await this.locationService.findByCoordinates(
        place.geometry.location.lat,
        place.geometry.location.lng,
        0.01, // 10 meter radius
      );

      if (existingLocations.length > 0) {
        return existingLocations[0];
      }

      // Parse address components
      const addressParts = this.parseGoogleAddress(place.formatted_address);

      // Create new location
      const locationData = {
        nameEn: place.name,
        nameAr: place.name, // TODO: Could implement translation service
        latitude: place.geometry.location.lat,
        longitude: place.geometry.location.lng,
        streetAddressEn: addressParts.streetAddress,
        streetAddressAr: addressParts.streetAddress,
        cityEn: addressParts.city,
        cityAr: addressParts.city,
        stateEn: addressParts.state,
        stateAr: addressParts.state,
        countryEn: addressParts.country,
        countryAr: addressParts.country,
        postalCode: addressParts.postalCode,
        placeId: place.place_id,
        mapUrl: `https://maps.google.com/?place_id=${place.place_id}`,
      };

      const location = await this.locationService.create(
        Location,
        locationData,
      );
      result.locationsCreated++;

      return location;
    } catch (error) {
      throw new Error(`Failed to create location: ${error.message}`);
    }
  }

  private parseGoogleAddress(formattedAddress: string): {
    streetAddress?: string;
    city?: string;
    state?: string;
    country?: string;
    postalCode?: string;
  } {
    const parts = formattedAddress.split(', ');

    // This is a simplified parser - in production you might want to use
    // Google's Geocoding API for more accurate address parsing
    return {
      streetAddress: parts[0] || undefined,
      city: parts[1] || undefined,
      state: parts[2] || undefined,
      country: parts[parts.length - 1] || undefined,
    };
  }

  private async extractAndCreateFeatures(
    place: GooglePlace,
    result: ImportPlacesResult,
  ): Promise<Feature[]> {
    const features: Feature[] = [];

    try {
      // Map Google place types to features
      const featureNames = this.mapGoogleTypesToFeatures(place.types);

      for (const featureName of featureNames) {
        try {
          // Check if feature already exists
          const existingFeatures = await this.featureService.getAll({
            searchKey: featureName,
            limit: 1,
            offset: 0,
          });

          let feature: Feature;
          if (existingFeatures.items.length > 0) {
            feature = existingFeatures.items[0];
          } else {
            // Create new feature
            feature = await this.featureService.create(Feature, {
              nameEn: featureName,
              nameAr: featureName, // TODO: Could implement translation
              icon: this.getFeatureIcon(featureName),
            });
            result.featuresCreated++;
          }

          features.push(feature);
        } catch (error) {
          this.logger.warn(
            `Failed to create feature ${featureName}: ${error.message}`,
          );
        }
      }
    } catch (error) {
      this.logger.warn(
        `Failed to extract features for place ${place.name}: ${error.message}`,
      );
    }

    return features;
  }

  private mapGoogleTypesToFeatures(types: string[]): string[] {
    const featureMapping: Record<string, string> = {
      wheelchair_accessible: 'Wheelchair Accessible',
      delivery: 'Delivery Available',
      takeout: 'Takeout Available',
      outdoor_seating: 'Outdoor Seating',
      wifi: 'Free WiFi',
      parking: 'Parking Available',
      air_conditioning: 'Air Conditioning',
      credit_cards: 'Credit Cards Accepted',
      reservations: 'Reservations Available',
    };

    const features: string[] = [];

    for (const type of types) {
      if (featureMapping[type]) {
        features.push(featureMapping[type]);
      }
    }

    // Add some default features based on place types
    if (types.includes('restaurant') || types.includes('food')) {
      features.push('Dine In');
    }

    if (types.includes('store') || types.includes('shopping_mall')) {
      features.push('Shopping');
    }

    return [...new Set(features)]; // Remove duplicates
  }

  private getFeatureIcon(featureName: string): string {
    const iconMapping: Record<string, string> = {
      'Wheelchair Accessible': '♿',
      'Delivery Available': '🚚',
      'Takeout Available': '🥡',
      'Outdoor Seating': '🌤️',
      'Free WiFi': '📶',
      'Parking Available': '🅿️',
      'Air Conditioning': '❄️',
      'Credit Cards Accepted': '💳',
      'Reservations Available': '📞',
      'Dine In': '🍽️',
      Shopping: '🛍️',
    };

    return iconMapping[featureName] || '⭐';
  }

  private async createBusiness(
    place: GooglePlace,
    category: Category,
    location: Location,
    features: Feature[],
    result: ImportPlacesResult,
  ): Promise<Business> {
    try {
      const businessData = {
        nameEn: place.name,
        nameAr: place.name, // TODO: Could implement translation
        phoneNumber: place.formatted_phone_number || undefined,
        locationId: location.id,
        primaryCategoryId: category.id,
        averageRating: place.rating || 0,
        totalReviewsCount: place.user_ratings_total || 0,
        isActive: false, // Set to false initially for review
        isVerified: false,
        isPremium: false,
        featureIds: features.map((f) => f.id),
      };

      const business = await this.businessService.create(
        Business,
        businessData,
      );
      result.businessesCreated++;

      return business;
    } catch (error) {
      throw new Error(`Failed to create business: ${error.message}`);
    }
  }

  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}
