import { ConfigurationValidationDto } from '@app/common';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Business } from '../../business/entities/business.entity';
import { IBusinessService } from '../../business/interfaces/business-service/business-service.interface';
import { Category } from '../../category/entities/category.entity';
import { ICategoryService } from '../../category/interfaces/category-service/category-service.interface';
import { Feature } from '../../feature/entities/feature.entity';
import { IFeatureService } from '../../feature/interfaces/feature-service/feature-service.interface';
import { Location } from '../../location/entities/location.entity';
import { ILocationService } from '../../location/interfaces/location-service/location-service.interface';
import { GooglePlace } from '../interfaces/google-place/google-place.interface';
import { GooglePlacesResponse } from '../interfaces/google-places-response/google-places-response.interface';
import { ImportPlacesParams } from '../interfaces/import-places-params/import-places-params.interface';
import { ImportPlacesResult } from '../interfaces/import-places-result/import-places-result.interface';
import { IUtilityService } from '../interfaces/utility-service/utility-service.interface';

@Injectable()
export class UtilityService implements IUtilityService {
  private readonly logger = new Logger(UtilityService.name);
  private readonly googlePlacesApiKey: string;
  private readonly googlePlacesBaseUrl: string;
  private readonly rateLimitDelay = 100; // 100ms delay between requests
  private readonly maxRetries = 3;

  // Mapping from Google Places types to database category IDs
  private readonly googlePlacesToCategoryMapping: Record<string, number> = {
    // Restaurants & Food (Category ID: 1)
    restaurant: 1,
    meal_takeaway: 1,
    meal_delivery: 1,
    food: 1,

    // Fast Food (Category ID: 6)
    fast_food: 6,

    // Fine Dining (Category ID: 7)
    fine_dining: 7,

    // Cafes (Category ID: 8)
    cafe: 8,
    coffee_shop: 8,

    // Shopping (Category ID: 2)
    store: 2,
    shopping_mall: 2,
    department_store: 2,
    supermarket: 2,
    grocery_or_supermarket: 2,

    // Clothing (Category ID: 9)
    clothing_store: 9,
    shoe_store: 9,

    // Electronics (Category ID: 10)
    electronics_store: 10,
    computer_store: 10,

    // Healthcare (Category ID: 3)
    health: 3,
    doctor: 3,
    dentist: 3,
    veterinary_care: 3,

    // Hospitals (Category ID: 11)
    hospital: 11,

    // Pharmacies (Category ID: 12)
    pharmacy: 12,

    // Entertainment (Category ID: 4)
    amusement_park: 4,
    movie_theater: 4,
    night_club: 4,
    casino: 4,
    bowling_alley: 4,

    // Services (Category ID: 5)
    bank: 5,
    atm: 5,
    gas_station: 5,
    car_repair: 5,
    hair_care: 5,
    beauty_salon: 5,
    spa: 5,
    gym: 5,
    laundry: 5,
    post_office: 5,

    // Bakery (Category ID: 13)
    bakery: 13,

    // Tailor (Category ID: 14)
    tailor: 14,

    // Building Supply Store (Category ID: 15)
    hardware_store: 15,
    home_goods_store: 15,
    furniture_store: 15,
  };

  constructor(
    private readonly configService: ConfigService<
      ConfigurationValidationDto,
      true
    >,
    @Inject(IBusinessService)
    private readonly businessService: IBusinessService,
    @Inject(ILocationService)
    private readonly locationService: ILocationService,
    @Inject(ICategoryService)
    private readonly categoryService: ICategoryService,
    @Inject(IFeatureService)
    private readonly featureService: IFeatureService,
  ) {
    this.googlePlacesApiKey =
      this.configService.get('GOOGLE_PLACES_API_KEY') || '';
    this.googlePlacesBaseUrl =
      this.configService.get('GOOGLE_PLACES_API_BASE_URL') ||
      'https://maps.googleapis.com/maps/api/place';

    if (!this.googlePlacesApiKey) {
      this.logger.warn(
        'Google Places API key not configured. Import functionality will not work.',
      );
    }
  }

  /**
   * Determines the best category for a Google Place based on its types
   * @param place Google Place object with types array
   * @returns Category ID or null if no match found
   */
  private determineCategoryForPlace(place: GooglePlace): number | null {
    if (!place.types || place.types.length === 0) {
      return null;
    }

    // Check each type in the place against our mapping
    for (const type of place.types) {
      if (this.googlePlacesToCategoryMapping[type]) {
        this.logger.debug(
          `Mapped Google Place type '${type}' to category ID ${this.googlePlacesToCategoryMapping[type]} for place: ${place.name}`,
        );
        return this.googlePlacesToCategoryMapping[type];
      }
    }

    // Log unmapped types for future improvement
    this.logger.debug(
      `No category mapping found for place '${place.name}' with types: ${place.types.join(', ')}`,
    );
    return null;
  }

  /**
   * Fetches all nearby places using common Google Places types
   * @param latitude Center latitude
   * @param longitude Center longitude
   * @param radius Search radius in kilometers
   * @returns Array of all unique places found
   */
  private async fetchAllNearbyPlaces(
    latitude: number,
    longitude: number,
    radius: number,
  ): Promise<GooglePlace[]> {
    // Common place types that cover most business categories
    const commonPlaceTypes = [
      'establishment', // General business establishments
      'point_of_interest', // Points of interest
      'store', // General stores
      'restaurant', // Restaurants
      'food', // Food establishments
      'health', // Healthcare
      'shopping_mall', // Shopping centers
      'gas_station', // Service stations
      'bank', // Financial services
      'pharmacy', // Pharmacies
      'hospital', // Hospitals
      'school', // Educational institutions
      'gym', // Fitness centers
      'beauty_salon', // Beauty services
      'car_repair', // Automotive services
      'electronics_store', // Electronics
      'clothing_store', // Clothing
      'supermarket', // Grocery stores
      'bakery', // Bakeries
      'cafe', // Cafes
    ];

    const allPlaces: GooglePlace[] = [];
    const seenPlaceIds = new Set<string>();

    for (const placeType of commonPlaceTypes) {
      try {
        this.logger.debug(`Fetching places for type: ${placeType}`);
        const places = await this.fetchGooglePlaces(
          latitude,
          longitude,
          radius,
          placeType,
        );

        this.logger.debug(
          `Found ${places.length} places for type: ${placeType}`,
        );

        // Add unique places (avoid duplicates)
        for (const place of places) {
          if (!seenPlaceIds.has(place.place_id)) {
            seenPlaceIds.add(place.place_id);
            allPlaces.push(place);
          }
        }

        // Rate limiting delay between different place type requests
        await this.delay(this.rateLimitDelay);
      } catch (error) {
        this.logger.error(
          `Failed to fetch places for type ${placeType}: ${error.message}`,
        );
      }
    }

    this.logger.log(
      `Fetched ${allPlaces.length} unique places from ${commonPlaceTypes.length} place types`,
    );
    return allPlaces;
  }

  async importPlacesFromGooglePlaces(
    params: ImportPlacesParams,
  ): Promise<ImportPlacesResult> {
    this.logger.log(
      `Starting Google Places import for coordinates: ${params.latitude}, ${params.longitude}`,
    );

    if (!this.googlePlacesApiKey) {
      throw new Error('Google Places API key not configured');
    }

    const result: ImportPlacesResult = {
      totalProcessed: 0,
      businessesCreated: 0,
      locationsCreated: 0,
      featuresCreated: 0,
      errors: [],
    };

    try {
      // Fetch all places from Google Places API using common place types
      this.logger.log(
        `Fetching places with API key configured: ${!!this.googlePlacesApiKey}`,
      );
      const allPlaces = await this.fetchAllNearbyPlaces(
        params.latitude,
        params.longitude,
        params.radius,
      );

      this.logger.log(
        `Found ${allPlaces.length} places from Google Places API`,
      );

      // Process each place and determine its category
      for (const place of allPlaces) {
        try {
          // Determine the best category for this place
          const categoryId = this.determineCategoryForPlace(place);

          if (!categoryId) {
            this.logger.debug(
              `Skipping place '${place.name}' - no matching category found`,
            );
            continue;
          }

          // Check if this category should be processed (if categoryIds filter is provided)
          if (params.categoryIds && params.categoryIds.length > 0) {
            if (!params.categoryIds.includes(categoryId)) {
              this.logger.debug(
                `Skipping place '${place.name}' - category ${categoryId} not in filter`,
              );
              continue;
            }
          }

          // Get the category object
          const category = await this.categoryService.findOneById(categoryId);
          if (!category) {
            this.logger.warn(
              `Category ${categoryId} not found for place '${place.name}'`,
            );
            continue;
          }

          await this.processPlace(place, category, result);
          result.totalProcessed++;

          // Rate limiting delay between places
          await this.delay(this.rateLimitDelay);
        } catch (error) {
          const errorMsg = `Error processing place ${place.name}: ${error.message}`;
          this.logger.error(errorMsg);
          result.errors.push(errorMsg);
        }
      }

      this.logger.log(
        `Import completed. Processed: ${result.totalProcessed}, Created: ${result.businessesCreated} businesses`,
      );
      return result;
    } catch (error) {
      this.logger.error('Fatal error during import:', error);
      result.errors.push(`Fatal error: ${error.message}`);
      return result;
    }
  }

  private async fetchGooglePlaces(
    latitude: number,
    longitude: number,
    radius: number,
    placeType: string,
  ): Promise<GooglePlace[]> {
    const url = `${this.googlePlacesBaseUrl}/nearbysearch/json`;
    const params = new URLSearchParams({
      location: `${latitude},${longitude}`,
      radius: (radius * 1000).toString(), // Convert km to meters
      type: placeType,
      key: this.googlePlacesApiKey,
    });

    let allPlaces: GooglePlace[] = [];
    let nextPageToken: string | undefined;
    let attempts = 0;

    do {
      if (nextPageToken) {
        params.set('pagetoken', nextPageToken);
        // Google requires a delay before using next page token
        await this.delay(2000);
      }

      this.logger.debug(`Making request to: ${url}?${params.toString()}`);
      const response = await this.makeGooglePlacesRequest(
        `${url}?${params.toString()}`,
      );

      this.logger.debug(
        `Google Places API response status: ${response.status}, results count: ${response.results?.length || 0}`,
      );

      if (response.status !== 'OK' && response.status !== 'ZERO_RESULTS') {
        throw new Error(
          `Google Places API error: ${response.status} - ${response.error_message || 'Unknown error'}`,
        );
      }

      allPlaces.push(...response.results);
      nextPageToken = response.next_page_token;
      attempts++;

      // Limit to prevent infinite loops and respect API quotas
      if (attempts >= 3) {
        break;
      }
    } while (nextPageToken);

    return allPlaces;
  }

  private async makeGooglePlacesRequest(
    url: string,
  ): Promise<GooglePlacesResponse> {
    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        const response = await fetch(url);

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return await response.json();
      } catch (error) {
        this.logger.warn(
          `Attempt ${attempt} failed for Google Places request: ${error.message}`,
        );

        if (attempt === this.maxRetries) {
          throw error;
        }

        // Exponential backoff
        await this.delay(1000 * Math.pow(2, attempt - 1));
      }
    }

    throw new Error('Max retries exceeded');
  }

  private async processPlace(
    place: GooglePlace,
    category: Category,
    result: ImportPlacesResult,
  ): Promise<void> {
    try {
      // Check if business already exists by place_id
      const existingLocation = await this.locationService.getAll(
        { limit: 1, offset: 0 } as any,
        { where: { placeId: place.place_id } },
      );

      if (existingLocation.items.length > 0) {
        this.logger.debug(`Place ${place.name} already exists, skipping`);
        return;
      }

      // Create or find location
      const location = await this.createOrFindLocation(place, result);

      // Extract features from place types
      const features = await this.extractAndCreateFeatures(place, result);

      // Create business
      const business = await this.createBusiness(
        place,
        category,
        location,
        features,
        result,
      );

      this.logger.debug(`Successfully created business: ${business.nameEn}`);
    } catch (error) {
      throw new Error(
        `Failed to process place ${place.name}: ${error.message}`,
      );
    }
  }

  private async createOrFindLocation(
    place: GooglePlace,
    result: ImportPlacesResult,
  ): Promise<Location> {
    try {
      // Check if location exists by coordinates (within small tolerance ~10 meters)
      const existingLocations = await this.locationService.findByCoordinates(
        place.geometry.location.lat,
        place.geometry.location.lng,
        0.01, // 10 meter radius
      );

      if (existingLocations.length > 0) {
        return existingLocations[0];
      }

      // Parse address components
      const addressParts = this.parseGoogleAddress(place.formatted_address);

      // Create new location
      const locationData = {
        nameEn: place.name,
        nameAr: place.name, // TODO: Could implement translation service
        latitude: place.geometry.location.lat,
        longitude: place.geometry.location.lng,
        streetAddressEn: addressParts.streetAddress,
        streetAddressAr: addressParts.streetAddress,
        cityEn: addressParts.city,
        cityAr: addressParts.city,
        stateEn: addressParts.state,
        stateAr: addressParts.state,
        countryEn: addressParts.country,
        countryAr: addressParts.country,
        postalCode: addressParts.postalCode,
        placeId: place.place_id,
        mapUrl: `https://maps.google.com/?place_id=${place.place_id}`,
      };

      const location = await this.locationService.create(
        Location,
        locationData,
      );
      result.locationsCreated++;

      return location;
    } catch (error) {
      throw new Error(`Failed to create location: ${error.message}`);
    }
  }

  private parseGoogleAddress(formattedAddress: string): {
    streetAddress?: string;
    city?: string;
    state?: string;
    country?: string;
    postalCode?: string;
  } {
    const parts = formattedAddress.split(', ');

    // This is a simplified parser - in production you might want to use
    // Google's Geocoding API for more accurate address parsing
    return {
      streetAddress: parts[0] || undefined,
      city: parts[1] || undefined,
      state: parts[2] || undefined,
      country: parts[parts.length - 1] || undefined,
    };
  }

  private async extractAndCreateFeatures(
    place: GooglePlace,
    result: ImportPlacesResult,
  ): Promise<Feature[]> {
    const features: Feature[] = [];

    try {
      // Map Google place types to features
      const featureNames = this.mapGoogleTypesToFeatures(place.types);

      for (const featureName of featureNames) {
        try {
          // Check if feature already exists
          const existingFeatures = await this.featureService.getAll({
            searchKey: featureName,
            limit: 1,
            offset: 0,
          });

          let feature: Feature;
          if (existingFeatures.items.length > 0) {
            feature = existingFeatures.items[0];
          } else {
            // Create new feature
            feature = await this.featureService.create(Feature, {
              nameEn: featureName,
              nameAr: featureName, // TODO: Could implement translation
              icon: this.getFeatureIcon(featureName),
            });
            result.featuresCreated++;
          }

          features.push(feature);
        } catch (error) {
          this.logger.warn(
            `Failed to create feature ${featureName}: ${error.message}`,
          );
        }
      }
    } catch (error) {
      this.logger.warn(
        `Failed to extract features for place ${place.name}: ${error.message}`,
      );
    }

    return features;
  }

  private mapGoogleTypesToFeatures(types: string[]): string[] {
    const featureMapping: Record<string, string> = {
      wheelchair_accessible: 'Wheelchair Accessible',
      delivery: 'Delivery Available',
      takeout: 'Takeout Available',
      outdoor_seating: 'Outdoor Seating',
      wifi: 'Free WiFi',
      parking: 'Parking Available',
      air_conditioning: 'Air Conditioning',
      credit_cards: 'Credit Cards Accepted',
      reservations: 'Reservations Available',
    };

    const features: string[] = [];

    for (const type of types) {
      if (featureMapping[type]) {
        features.push(featureMapping[type]);
      }
    }

    // Add some default features based on place types
    if (types.includes('restaurant') || types.includes('food')) {
      features.push('Dine In');
    }

    if (types.includes('store') || types.includes('shopping_mall')) {
      features.push('Shopping');
    }

    return [...new Set(features)]; // Remove duplicates
  }

  private getFeatureIcon(featureName: string): string {
    const iconMapping: Record<string, string> = {
      'Wheelchair Accessible': '♿',
      'Delivery Available': '🚚',
      'Takeout Available': '🥡',
      'Outdoor Seating': '🌤️',
      'Free WiFi': '📶',
      'Parking Available': '🅿️',
      'Air Conditioning': '❄️',
      'Credit Cards Accepted': '💳',
      'Reservations Available': '📞',
      'Dine In': '🍽️',
      Shopping: '🛍️',
    };

    return iconMapping[featureName] || '⭐';
  }

  private async createBusiness(
    place: GooglePlace,
    category: Category,
    location: Location,
    features: Feature[],
    result: ImportPlacesResult,
  ): Promise<Business> {
    try {
      const businessData = {
        nameEn: place.name,
        nameAr: place.name, // TODO: Could implement translation
        phoneNumber: place.formatted_phone_number || undefined,
        locationId: location.id,
        primaryCategoryId: category.id,
        averageRating: place.rating || 0,
        totalReviewsCount: place.user_ratings_total || 0,
        isActive: false, // Set to false initially for review
        isVerified: false,
        isPremium: false,
        featureIds: features.map((f) => f.id),
      };

      const business = await this.businessService.create(
        Business,
        businessData,
      );
      result.businessesCreated++;

      return business;
    } catch (error) {
      throw new Error(`Failed to create business: ${error.message}`);
    }
  }

  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}
