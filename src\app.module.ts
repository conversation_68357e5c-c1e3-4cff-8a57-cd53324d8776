import {
  AllExceptionsFilter,
  APP_NAME,
  ConfigurationValidationDto,
  getWinstonLoggerInstance,
  ResponseInterceptor,
} from '@app/common';
import { Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { APP_GUARD } from '@nestjs/core';
import { WinstonModule } from 'nest-winston';
import { DotEnvConfiguration } from './config/configuration';
import { DatabaseModule } from './infrastructure/database/database.module';
import { AuthModule } from './modules/auth/auth.module';
import { JwtAuthGuard } from './modules/auth/guards/jwt-auth.guard';
import { BusinessModule } from './modules/business';
import { CategoryModule } from './modules/category/category.module';
import { DashboardModule } from './modules/dashboard';
import { FeatureModule } from './modules/feature/feature.module';
import { LocationModule } from './modules/location';

@Module({
  imports: [
    DotEnvConfiguration.init<ConfigurationValidationDto>(
      ConfigurationValidationDto,
      './.env',
    ),
    WinstonModule.forRootAsync({
      imports: undefined,
      inject: [ConfigService<ConfigurationValidationDto, true>],
      useFactory: (
        configService: ConfigService<ConfigurationValidationDto, true>,
      ) => getWinstonLoggerInstance(APP_NAME, configService),
    }),
    DatabaseModule,
    AuthModule,
    CategoryModule,
    FeatureModule,
    LocationModule,
    BusinessModule,
    DashboardModule,
    UtilityModule,
  ],
  controllers: [],
  providers: [
    AllExceptionsFilter,
    ResponseInterceptor,
    {
      provide: APP_GUARD,
      useExisting: JwtAuthGuard,
    },
  ],
})
export class AppModule {}
